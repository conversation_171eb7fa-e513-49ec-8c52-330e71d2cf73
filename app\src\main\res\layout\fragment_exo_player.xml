<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:gravity="center"
    android:keepScreenOn="true">

    <com.voicebird.tv.utils.player.CustomPlayerView
        android:id="@+id/exoPlayerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        app:played_color="#E62E2F"
        app:buffered_color="#FF9C9C"
        app:unplayed_color="#B3B3B3"
        app:controller_layout_id="@layout/custom_controls"/>

    <androidx.nemosofts.material.ProgressBarView
        android:id="@+id/pb_player"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:layout_centerInParent="true"
        android:indeterminate="true"
        app:pb_color="#FFee44"
        app:pb_colors="@array/progress_colors"
        app:pb_max_sweep_angle="300"
        app:pb_min_sweep_angle="10"
        app:pb_rotation_speed="1.0"
        app:pb_stroke_width="@dimen/_2sdp"
        app:pb_sweep_speed="1.0" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_try_again"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:paddingLeft="@dimen/_20sdp"
        android:paddingRight="@dimen/_20sdp"
        android:text="@string/try_again"
        android:textColor="@color/white"
        android:visibility="gone"
        app:backgroundTint="?ns_primary" />

</RelativeLayout>
