<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.ForgotPasswordActivity"
    android:background="?ns_bg">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="@drawable/ic_close"
        app:title="@string/forgot_pass"
        app:titleCentered="true" />

    <LinearLayout
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_80sdp"
            android:contentDescription="@string/todo"
            android:src="@drawable/ic_forgot" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:text="@string/forgot_pass"
            android:textAlignment="center"
            android:textColor="?ns_bg_dark"
            android:textSize="@dimen/_20ssp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/forgot_password_message"
            android:textColor="?ns_title_sub"
            android:textSize="@dimen/_12ssp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:background="@drawable/btn_border"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/_2sdp">

            <ImageView
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:contentDescription="@string/todo"
                android:padding="@dimen/_9sdp"
                android:src="@drawable/ic_email"
                app:tint="?ns_title_sub" />

            <EditText
                android:id="@+id/et_forgot_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="@string/email_id"
                android:importantForAutofill="no"
                android:inputType="textEmailAddress"
                android:maxLines="1"
                android:padding="@dimen/_5sdp"
                android:textColor="?ns_title"
                android:textColorHint="?ns_title_sub"
                android:textSize="@dimen/_12ssp" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="?ns_bg"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_btn_forgot_send"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_35sdp"
            android:background="@drawable/btn_primary"
            android:layout_margin="@dimen/_10sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                app:tint="@color/white"
                android:padding="@dimen/_1sdp"
                android:src="@drawable/ic_send_plane"
                android:layout_marginEnd="@dimen/_5sdp"
                android:contentDescription="@string/todo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/submit"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp" />

        </LinearLayout>

        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/ll_adView"
            android:background="?ns_bg"
            android:backgroundTint="?ns_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

</RelativeLayout>