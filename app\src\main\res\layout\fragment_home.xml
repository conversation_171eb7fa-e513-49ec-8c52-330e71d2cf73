<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="?ns_bg">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="15dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_home"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/fl_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:layout_marginBottom="?attr/actionBarSize" />

        <androidx.nemosofts.material.ProgressBarView
            android:id="@+id/pb_home"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="@dimen/_40sdp"
            android:layout_centerInParent="true"
            android:indeterminate="true"
            app:pb_color="#FFee44"
            app:pb_colors="@array/progress_colors"
            app:pb_max_sweep_angle="300"
            app:pb_min_sweep_angle="10"
            app:pb_rotation_speed="1.0"
            app:pb_stroke_width="@dimen/_2sdp"
            app:pb_sweep_speed="1.0" />

    </RelativeLayout>

</androidx.core.widget.NestedScrollView>