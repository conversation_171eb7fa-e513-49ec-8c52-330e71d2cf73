<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_3sdp"
    android:id="@+id/rl_live_tv"
    android:focusable="true"
    android:focusableInTouchMode="false"
    android:background="@drawable/bg_card_selector"
    android:padding="@dimen/_1sdp">

    <androidx.nemosofts.material.ImageHelperView
        android:id="@+id/iv_live_tv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_110sdp"
        android:layout_alignParentTop="true"
        android:padding="@dimen/_1sdp"
        android:scaleType="centerCrop"
        android:src="@drawable/material_design_default"
        app:hv_corner_radius_top_left="@dimen/_5sdp"
        app:hv_corner_radius_top_right="@dimen/_5sdp" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_live_tv"
        android:lines="1"
        android:padding="@dimen/_5sdp"
        android:text="@string/app_name"
        android:textAlignment="center"
        android:textColor="?ns_title"
        android:textSize="@dimen/_12ssp" />

</RelativeLayout>