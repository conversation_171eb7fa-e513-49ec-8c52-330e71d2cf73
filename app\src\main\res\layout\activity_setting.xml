<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.SettingActivity"
    android:background="?ns_bg"
    android:orientation="vertical">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="?ns_icon_back"
        app:title="@string/settings"
        app:titleCentered="true" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_marginStart="@dimen/_10sdp">

            <LinearLayout
                android:layout_marginTop="@dimen/_5sdp"
                android:paddingTop="@dimen/_10sdp"
                android:paddingBottom="@dimen/_10sdp"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_about_border">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingEnd="@dimen/_10sdp"
                    android:paddingStart="0dp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:layout_gravity="center"
                        android:padding="@dimen/_8sdp"
                        android:src="@drawable/ic_sun"
                        app:tint="@color/color_setting_1"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_weight="1"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp">

                        <TextView
                            android:textStyle="bold"
                            android:textAlignment="viewStart"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:text="@string/app_theme"
                            android:textSize="@dimen/_13ssp"
                            android:textColor="?ns_title"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:textAlignment="viewStart"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/dark_or_light_mode"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_11ssp"/>

                    </LinearLayout>

                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_marginTop="@dimen/_minus4sdp"
                            android:layout_marginBottom="@dimen/_3sdp"
                            android:id="@+id/iv_dark_mode"
                            android:scaleType="fitEnd"
                            android:src="@drawable/classic"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_70sdp"
                            android:contentDescription="@string/todo" />

                    </LinearLayout>

                </LinearLayout>
                <LinearLayout
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_23sdp">

                    <TextView
                        android:id="@+id/tv_classic"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/btn_accent"
                        android:gravity="center"
                        android:text="@string/theme_classic"
                        android:textAlignment="center"
                        android:textColor="?ns_bg"
                        android:textSize="@dimen/_11ssp"
                        tools:ignore="TextContrastCheck" />

                    <TextView
                        android:id="@+id/tv_dark_grey"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="@dimen/_2sdp"
                        android:layout_marginEnd="@dimen/_2sdp"
                        android:layout_weight="1"
                        android:background="@drawable/btn_border_bg"
                        android:gravity="center"
                        android:text="@string/theme_dark_grey"
                        android:textAlignment="center"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_11ssp" />

                    <TextView
                        android:id="@+id/tv_dark_blue"
                        android:gravity="center"
                        android:background="@drawable/btn_border_bg"
                        android:textAlignment="center"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="@string/theme_dark_blue"
                        android:textSize="@dimen/_11ssp"
                        android:textColor="?ns_title" />

                    <TextView
                        android:id="@+id/tv_dark"
                        android:gravity="center"
                        android:background="@drawable/btn_border_bg"
                        android:textAlignment="center"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="@string/theme_dark"
                        android:textSize="@dimen/_11ssp"
                        android:textColor="?ns_title"
                        android:layout_marginStart="@dimen/_2sdp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_about_border">

                <LinearLayout
                    android:id="@+id/ll_cache"
                    android:background="?attr/selectableItemBackground"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_15sdp"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:padding="@dimen/_9sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_clean_code"
                        app:tint="@color/color_setting_2"
                        android:contentDescription="@string/todo"/>

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:text="@string/cache"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/clear_cache"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_11ssp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_cachesize"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/_40sdp"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/_10sdp"
                        android:paddingEnd="@dimen/_10sdp"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="?ns_title" />

                    <ImageView
                        android:layout_marginEnd="@dimen/_20sdp"
                        android:layout_width="@dimen/_25sdp"
                        android:layout_height="@dimen/_25sdp"
                        android:src="@drawable/ic_clean_code"
                        android:padding="@dimen/_3sdp"
                        android:layout_gravity="center_vertical"
                        app:tint="?ns_bg_dark"
                        android:contentDescription="@string/todo" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_about_border">

                <LinearLayout
                    android:id="@+id/ll_notifications"
                    android:background="?attr/selectableItemBackground"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_15sdp"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:padding="@dimen/_9sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_notification"
                        app:tint="@color/color_setting_3"
                        android:contentDescription="@string/todo"/>

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:text="@string/notification"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/to_receive_notification"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_11ssp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_marginEnd="@dimen/_3sdp"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?ns_title_sub"
                        android:padding="@dimen/_2sdp"
                        android:contentDescription="@string/todo"/>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_about_border">

                <LinearLayout
                    android:id="@+id/ll_privacy_data"
                    android:background="?attr/selectableItemBackground"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_15sdp"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:padding="@dimen/_9sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_shield_user"
                        app:tint="@color/color_setting_6"
                        android:contentDescription="@string/todo"/>

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:text="@string/deletion_policy"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/completely_delete_you_account"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_11ssp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_marginEnd="@dimen/_3sdp"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?ns_title_sub"
                        android:padding="@dimen/_2sdp"
                        android:contentDescription="@string/todo"/>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginBottom="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_about_border">

                <!-- Privacy -->
                <LinearLayout
                    android:id="@+id/ll_privacy"
                    android:background="?attr/selectableItemBackground"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_15sdp"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:padding="@dimen/_9sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_shield_keyhole"
                        app:tint="@color/color_setting_7"
                        android:contentDescription="@string/todo"/>

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:text="@string/privacy_policy"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/this_app_privacy_policy"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_11ssp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_marginEnd="@dimen/_3sdp"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?ns_title_sub"
                        android:padding="@dimen/_2sdp"
                        android:contentDescription="@string/todo"/>

                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border"/>

                <!-- Terms -->
                <LinearLayout
                    android:id="@+id/ll_terms"
                    android:background="?attr/selectableItemBackground"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_15sdp"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:padding="@dimen/_9sdp"
                        android:layout_gravity="top"
                        android:src="@drawable/ic_todo"
                        app:tint="@color/color_setting_1"
                        android:contentDescription="@string/todo"/>

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:text="@string/terms_and_conditions"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/this_app_terms"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_11ssp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_marginEnd="@dimen/_3sdp"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?ns_title_sub"
                        android:padding="@dimen/_2sdp"
                        android:contentDescription="@string/todo"/>

                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border"/>

                <!-- About -->
                <LinearLayout
                    android:id="@+id/ll_about"
                    android:background="?attr/selectableItemBackground"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/_5sdp">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginEnd="@dimen/_15sdp"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:padding="@dimen/_9sdp"
                        android:layout_gravity="top"
                        android:src="@drawable/ic_information"
                        app:tint="@color/color_setting_2"
                        android:contentDescription="@string/todo"/>

                    <LinearLayout
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:text="@string/about"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/social_links"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_11ssp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_marginEnd="@dimen/_3sdp"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_gravity="center"
                        android:padding="@dimen/_2sdp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="?ns_title_sub"
                        android:contentDescription="@string/todo"/>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>