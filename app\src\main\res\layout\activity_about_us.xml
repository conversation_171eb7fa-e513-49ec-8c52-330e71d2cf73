<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.AboutUsActivity"
    android:background="?ns_bg"
    android:orientation="vertical">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:navigationIcon="?ns_icon_back"
        app:navigationIconTint="?ns_bg_dark"
        app:title="@string/about"
        app:titleCentered="true"
        app:titleTextColor="?ns_bg_dark"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_marginStart="@dimen/_10sdp">

            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_about_border">

                <androidx.nemosofts.material.ImageHelperView
                    android:id="@+id/logo"
                    android:layout_width="@dimen/_75sdp"
                    android:layout_height="@dimen/_75sdp"
                    android:contentDescription="@string/todo"
                    android:src="@drawable/logo"
                    app:hv_corner_radius_bottom_left="@dimen/_10sdp"
                    app:hv_corner_radius_top_left="@dimen/_10sdp"
                    android:background="?ns_bg"/>

                <View
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:layout_width="@dimen/_1sdp"
                    android:layout_height="match_parent"
                    android:background="?ns_border"/>

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_app_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/app_name"
                        android:textAlignment="viewStart"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_16ssp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_app_des"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLines="4"
                        android:text="@string/love_this_app"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="?ns_title_sub"
                        android:textSize="@dimen/_12ssp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/_5sdp"
                android:paddingTop="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_about_border">

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_building"
                        android:padding="@dimen/_9sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/company"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_company"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_name"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:id="@+id/ll_email"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_mail"
                        android:padding="@dimen/_10sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/email"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_email"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_name"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:id="@+id/ll_domain"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_global"
                        android:padding="@dimen/_10sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/website"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_website"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_name"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:id="@+id/ll_contact"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_phone"
                        android:padding="@dimen/_10sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/contact"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_contact"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_name"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/_5sdp"
                android:paddingTop="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_about_border">

                <LinearLayout
                    android:id="@+id/ll_more"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_share_bot"
                        android:padding="@dimen/_10sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/more_apps"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_marginEnd="@dimen/_5sdp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/share_the_app"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/_5sdp"
                android:paddingTop="@dimen/_5sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_about_border">

                <LinearLayout
                    android:id="@+id/ll_rate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_rating"
                        android:padding="@dimen/_10sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/rate_the_app"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/love_this_app"
                            android:layout_marginEnd="@dimen/_5sdp"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:id="@+id/ll_share"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_share_bot"
                        android:padding="@dimen/_10sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/share"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_marginEnd="@dimen/_5sdp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/share_the_app"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/ic_information"
                        android:padding="@dimen/_10sdp"
                        app:tint="?ns_title"
                        android:contentDescription="@string/todo" />

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/version"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title"
                            android:textSize="@dimen/_13ssp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_version"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_name"
                            android:textAlignment="viewStart"
                            android:textColor="?ns_title_sub"
                            android:textSize="@dimen/_12ssp"
                            android:layout_marginTop="@dimen/_1sdp"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10sdp"
                android:text="@string/made_with_in"
                android:textAlignment="center"
                android:textColor="?ns_title_sub"
                android:textStyle="bold" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>