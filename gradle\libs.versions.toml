[versions]
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
material = "1.12.0"
activity = "1.9.3"
constraintlayout = "2.2.0"
library = "9.0.4"
multidex = "2.0.1"
playServicesCastFramework = "22.0.0"
recyclerview = "1.3.2"
palette = "1.0.0"
commonsIo = "2.18.0"
gson = "2.11.0"
okhttp = "4.12.0"
picasso = "2.71828"

appReview = "2.0.2"
servicesAuth = "21.3.0"
firebaseCore = "21.1.1"
firebaseAuth = "23.1.0"
firebaseBom = "33.7.0"
firebaseInappmessagingDisplay = "21.0.1"
onesignal = "5.1.26"
scytale = "1.0.1"
pageindicatorview = "1.0.0"
eventbus = "3.3.1"

# Use stable version instead of alpha to prevent crashes
media3Exoplayer = "1.5.0"

wortise = "1.6.0"
adqualitysdk = "7.22.2"
applovinAds = "13.0.1"
audienceNetworkSdk = "6.18.0"
facebook = "6.18.0.0"
facebookadapter = "4.3.48"
inferAnnotation = "0.18.0"
startapp = "5.1.0"
mediationsdk = "8.5.0"
yandexAds = "7.8.1"
admobAds = "23.6.0"
unityAds = "4.12.5"

billing = "7.1.1"

lifecycle = "2.8.7"

[libraries]
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
library = { module = "com.github.nemosofts:library", version.ref = "library" }
multidex = { module = "androidx.multidex:multidex", version.ref = "multidex" }
play-cast-framework = { module = "com.google.android.gms:play-services-cast-framework", version.ref = "playServicesCastFramework" }
recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
palette = { module = "androidx.palette:palette", version.ref = "palette" }
commons = { module = "commons-io:commons-io", version.ref = "commonsIo" }

gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okhttp-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp" }
picasso = { module = "com.squareup.picasso:picasso", version.ref = "picasso" }

app-review = { module = "com.google.android.play:review", version.ref = "appReview" }
services-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "servicesAuth" }
firebase-core = { module = "com.google.firebase:firebase-core", version.ref = "firebaseCore" }
firebase-auth = { module = "com.google.firebase:firebase-auth", version.ref = "firebaseAuth" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics", version = "22.1.2" }
firebase-inappmessaging = { module = "com.google.firebase:firebase-inappmessaging-display", version.ref = "firebaseInappmessagingDisplay" }
oneSignal = { module = "com.onesignal:OneSignal", version.ref = "onesignal" }
scytale = { module = "com.yakivmospan:scytale", version.ref = "scytale" }
pageindicatorview = { module = "com.github.nemosofts:PageIndicatorView", version.ref = "pageindicatorview" }
eventbus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }

media3-session = { module = "androidx.media3:media3-session", version.ref = "media3Exoplayer" }
media3-datasource = { module = "androidx.media3:media3-datasource", version.ref = "media3Exoplayer" }
media3-decoder = { module = "androidx.media3:media3-decoder", version.ref = "media3Exoplayer" }
media3-common = { module = "androidx.media3:media3-common", version.ref = "media3Exoplayer" }
media3-container = { module = "androidx.media3:media3-container", version.ref = "media3Exoplayer" }
exoplayer-dash = { module = "androidx.media3:media3-exoplayer-dash", version.ref = "media3Exoplayer"}
exoplayer-hls = { module = "androidx.media3:media3-exoplayer-hls", version.ref = "media3Exoplayer" }
exoplayer-rtsp = { module = "androidx.media3:media3-exoplayer-rtsp", version.ref = "media3Exoplayer" }
exoplayer-smoothstreaming = { module = "androidx.media3:media3-exoplayer-smoothstreaming", version.ref = "media3Exoplayer"}

wortise = { module = "com.wortise:android-sdk", version.ref = "wortise" }
startapp = { module = "com.startapp:inapp-sdk", version.ref = "startapp" }
unitys = { module = "com.unity3d.ads:unity-ads", version.ref = "unityAds" }
applovin = { module = "com.applovin:applovin-sdk", version.ref = "applovinAds" }
yandex = { module = "com.yandex.android:mobileads", version.ref = "yandexAds" }
audience-network = { module = "com.facebook.android:audience-network-sdk", version.ref = "audienceNetworkSdk" }
facebook-annotation = { module = "com.facebook.infer.annotation:infer-annotation", version.ref = "inferAnnotation" }
facebook-mediation = { module = "com.google.ads.mediation:facebook", version.ref = "facebook" }
admob = { module = "com.google.android.gms:play-services-ads", version.ref = "admobAds" }
ironsource = { module = "com.ironsource.sdk:mediationsdk", version.ref = "mediationsdk" }
ironsource-adquality = { module = "com.ironsource:adqualitysdk", version.ref = "adqualitysdk" }
ironsource-facebook = { module = "com.ironsource.adapters:facebookadapter", version.ref = "facebookadapter" }

billingclient = { module = "com.android.billingclient:billing", version.ref = "billing" }

lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "lifecycle" }
lifecycle-runtime = { module = "androidx.lifecycle:lifecycle-runtime", version.ref = "lifecycle" }
lifecycle-compiler = { module = "androidx.lifecycle:lifecycle-compiler", version.ref = "lifecycle" }

[bundles]
nemosofts = [
    "junit",
    "ext-junit",
    "espresso-core",
    "appcompat",
    "material",
    "activity",
    "constraintlayout",
    "library",
    "multidex",
    "recyclerview",
    "palette",
    "commons"
]
network-security = [
    "gson",
    "okhttp",
    "okhttp-interceptor",
    "picasso"
]
google = [
    "app-review",
    "services-auth",
    "firebase-core",
    "firebase-auth",
    "firebase-analytics",
    "firebase-inappmessaging"
]
media3 = [
    "media3-session",
    "media3-datasource",
    "media3-decoder",
    "media3-common",
    "media3-container"
]
exoplayer = [
    "exoplayer-dash",
    "exoplayer-hls",
    "exoplayer-rtsp",
    "exoplayer-smoothstreaming"
]
advertising =[
    "startapp",
    "unitys",
    "applovin",
    "yandex",
    "audience-network",
    "facebook-annotation",
    "facebook-mediation",
    "admob",
    "ironsource",
    "ironsource-adquality",
    "ironsource-facebook"
]