<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.PostIDActivity"
    android:background="?ns_bg">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="?ns_icon_back"
        app:titleCentered="true" />

    <androidx.recyclerview.widget.RecyclerView
        android:layout_below="@id/toolbar"
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:layoutAnimation="@anim/layout_animation_from_bottom"/>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="50dp"
        android:src="@drawable/ic_arrow_up"
        android:tint="@color/white"
        android:visibility="invisible"
        app:fabSize="normal"
        android:contentDescription="@string/todo" />

    <FrameLayout
        android:id="@+id/fl_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_marginBottom="?attr/actionBarSize" />

    <androidx.nemosofts.material.ProgressBarView
        android:id="@+id/pb"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:layout_centerInParent="true"
        android:indeterminate="true"
        app:pb_color="#FFee44"
        app:pb_colors="@array/progress_colors"
        app:pb_max_sweep_angle="300"
        app:pb_min_sweep_angle="10"
        app:pb_rotation_speed="1.0"
        app:pb_stroke_width="@dimen/_2sdp"
        app:pb_sweep_speed="1.0" />

</RelativeLayout>