package com.voicebird.tv.utils.purchases.enums;

public enum ErrorType {
    CLIENT_NOT_READY,
    CLIENT_DISCONNECTED,
    PRODUCT_NOT_EXIST,
    CONSUME_ERROR,
    CONSUME_WARNING,
    <PERSON>K<PERSON><PERSON><PERSON>D<PERSON>_ERROR,
    <PERSON>K<PERSON><PERSON><PERSON>DGE_WARNING,
    FETCH_PURCHASED_PRODUCTS_ERROR,
    BIL<PERSON>ING_ERROR,
    USER_CANCELED,
    SERVICE_UNAVAILABLE,
    BILLING_UNAVAILABLE,
    ITEM_UNAVAILABLE,
    DEVELOPER_ERROR,
    ERROR,
    ITEM_ALREADY_OWNED,
    ITEM_NOT_OWNED
}