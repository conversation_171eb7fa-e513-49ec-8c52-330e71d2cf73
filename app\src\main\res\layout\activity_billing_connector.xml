<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.BillingConnectorActivity"
    android:background="?ns_bg_sub">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="?ns_icon_back"
        app:title="@string/subscription_plan"
        app:titleCentered="true" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_bottom"
        android:layout_below="@id/toolbar"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/lytPlan"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_55sdp"
            android:layout_margin="@dimen/_10ssp"
            android:background="@drawable/bg_card"
            tools:ignore="UselessParent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/_10ssp"
                android:layout_marginEnd="@dimen/_10ssp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textPrice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:includeFontPadding="false"
                        android:text="99"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_20ssp"
                        android:textStyle="bold"
                        tools:ignore="HardcodedText" />

                    <TextView
                        android:id="@+id/textCurrency"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/_5ssp"
                        android:includeFontPadding="false"
                        android:text="RS"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_12ssp"
                        tools:ignore="HardcodedText" />

                    <TextView
                        android:id="@+id/textDay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:includeFontPadding="false"
                        android:text="For 1 Month"
                        android:textColor="#ff0532"
                        android:textSize="@dimen/_11ssp"
                        tools:ignore="HardcodedText" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textPackName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="All Access Pack"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_11ssp"
                    tools:ignore="HardcodedText" />

            </LinearLayout>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_marginStart="@dimen/_10sdp"
            android:background="@drawable/bg_card"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/_10sdp"
                android:paddingTop="@dimen/_6sdp"
                android:paddingEnd="@dimen/_10sdp"
                android:paddingBottom="@dimen/_6sdp"
                android:text="@string/user_info"
                android:textColor="?ns_title"
                android:textSize="@dimen/_13ssp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/_1sdp"
                android:background="?ns_border" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/choose_plan"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_12ssp" />

                <TextView
                    android:id="@+id/choosePlanName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:includeFontPadding="false"
                    android:text="@string/choose_plan"
                    android:textColor="#ff0532"
                    android:textSize="@dimen/_12ssp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="@string/choose_plan_1"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_12ssp" />

                <TextView
                    android:id="@+id/planEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:text="@string/choose_plan"
                    android:textColor="#ff0532"
                    android:textSize="@dimen/_12ssp" />

            </LinearLayout>

            <TextView
                android:id="@+id/changePlan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="@dimen/_10sdp"
                android:background="@drawable/btn_danger"
                android:paddingStart="@dimen/_10sdp"
                android:paddingTop="@dimen/_5sdp"
                android:paddingEnd="@dimen/_10sdp"
                android:paddingBottom="@dimen/_5sdp"
                android:text="@string/change_plan"
                android:textColor="?ns_white"
                android:textSize="@dimen/_12ssp" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1sdp"
        android:layout_below="@id/toolbar"
        android:background="?ns_border" />

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="?ns_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_terms"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:background="?attr/selectableItemBackground"
                android:text="@string/terms_and_conditions"
                android:textColor="?colorAccent"
                android:textSize="@dimen/_11ssp" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/ll_calculator_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?ns_bg">

            <TextView
                android:id="@+id/tv_btn_proceed"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_35sdp"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:background="@drawable/btn_danger"
                android:gravity="center"
                android:text="@string/subscription_proceed"
                android:textColor="?ns_white"
                android:textSize="@dimen/_13ssp"
                android:textStyle="bold" />

        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1sdp"
        android:layout_above="@+id/ll_bottom"
        android:background="?ns_border" />

</RelativeLayout>