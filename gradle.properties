# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
#android.enableDexingArtifactTransform=false
org.gradle.warning.mode=none
org.gradle.unsafe.configuration-cache=true
#android.defaults.buildfeatures.buildconfig=true
android.nonFinalResIds=true
android.suppressUnsupportedCompileSdk=35
#---------------------------------------------------------------------------------------------------

BASE_URL="https://live.voicebirdtv.in/"
#Your admin panel BASE URL

API_NAME="NEMOSOFTS_APP"
#choose any word as API_NAME or also it is optional if you dont want to change keep NEMOSOFTS_APP as it is
#Warning :- Warning :- If you change this here then you must update admin panel php also. Update this files api.php and lb_helper.php.
#If you need then change otherwise leave it as.

ENC_KEY="onlineenc"
#choose any word as ENC_KEY or also it is optional if you dont want to change keep onlineenc as it is