<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/_110sdp"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_5sdp"
    android:layout_marginTop="@dimen/_5sdp"
    android:orientation="vertical"
    android:background="@drawable/bg_home_card">

    <RelativeLayout
        android:layout_width="@dimen/_110sdp"
        android:layout_height="@dimen/_110sdp">

        <androidx.nemosofts.material.ImageHelperView
            android:id="@+id/iv_cat"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/material_design_default"
            android:padding="@dimen/_1sdp"
            app:hv_corner_radius="@dimen/_3sdp"
            android:layout_margin="@dimen/_3sdp" />

        <ImageView
            android:layout_width="@dimen/_45sdp"
            android:layout_height="@dimen/_45sdp"
            android:layout_centerInParent="true"
            android:background="@drawable/abs_history_playlist"
            android:backgroundTint="#4B000000"
            android:contentDescription="@string/todo" />

        <ImageView
            android:layout_width="@dimen/_35sdp"
            android:layout_height="@dimen/_35sdp"
            android:layout_centerInParent="true"
            android:background="@drawable/abs_history_playlist"
            android:backgroundTint="#37000000"
            android:contentDescription="@string/todo"
            android:padding="@dimen/_6sdp"
            android:src="@drawable/ic_folders"
            app:tint="@color/white" />

    </RelativeLayout>

    <TextView
        android:padding="@dimen/_4sdp"
        android:layout_marginStart="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_5sdp"
        android:id="@+id/tv_cat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:textAlignment="center"
        android:lineSpacingMultiplier="0.9"
        android:maxLines="1"
        android:text="@string/app_name"
        android:textColor="?ns_title"
        android:textSize="@dimen/_11ssp"/>

</LinearLayout>