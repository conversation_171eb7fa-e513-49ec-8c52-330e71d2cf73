pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url "https://maven.google.com" }
        maven { url 'https://android-sdk.is.com/' }
        maven { url 'https://plugins.gradle.org/m2/' }
        maven { url 'https://maven.wortise.com/artifactory/public' }
        maven { url 'https://artifact.bytedance.com/repository/pangle' }
        maven { url 'https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea' }
    }
}

rootProject.name = "Online Live TV v10.3"
include ':app'