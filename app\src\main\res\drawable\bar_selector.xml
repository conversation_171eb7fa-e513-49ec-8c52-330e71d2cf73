<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle" >
            <solid android:color="?ns_border" />
            <corners android:radius="500dp"/>
            <padding android:bottom="@dimen/_5sdp"
                android:left="@dimen/_5sdp"
                android:right="@dimen/_5sdp"
                android:top="@dimen/_5sdp"/>
        </shape>
    </item>
    <item android:drawable="@drawable/transparent" />
</selector>