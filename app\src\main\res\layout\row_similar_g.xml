<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_home_item"
    android:layout_width="match_parent"
    android:background="@drawable/bg_home_card"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_5sdp">

    <androidx.nemosofts.material.ImageHelperView
        android:id="@+id/iv_similar_img"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_110sdp"
        android:scaleType="centerCrop"
        android:src="@drawable/material_design_default"
        android:padding="@dimen/_1sdp"
        app:hv_corner_radius="@dimen/_3sdp"
        android:layout_margin="@dimen/_3sdp" />

    <TextView
        android:id="@+id/tv_similar_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_similar_img"
        android:lines="1"
        android:padding="@dimen/_5sdp"
        android:text="@string/app_name"
        android:textAlignment="center"
        android:textColor="?ns_title"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:id="@+id/tv_similar_pre"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_1sdp"
        android:layout_marginTop="@dimen/_1sdp"
        android:background="@drawable/bg_pre_home"
        android:layoutDirection="ltr"
        android:paddingStart="@dimen/_5sdp"
        android:paddingEnd="@dimen/_5sdp"
        android:paddingBottom="@dimen/_2sdp"
        android:text="@string/premium"
        android:textColor="@color/white"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold" />

</RelativeLayout>