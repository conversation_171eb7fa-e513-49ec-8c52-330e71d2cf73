<vector android:autoMirrored="true" android:height="24dp"
    android:tint="#000000" android:viewportHeight="24"
    android:viewportWidth="24" android:width="24dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="@android:color/white" android:pathData="M16,10L8,10c-0.55,0 -1,0.45 -1,1s0.45,1 1,1h8c0.55,0 1,-0.45 1,-1s-0.45,-1 -1,-1zM19,3h-1L18,2c0,-0.55 -0.45,-1 -1,-1s-1,0.45 -1,1v1L8,3L8,2c0,-0.55 -0.45,-1 -1,-1s-1,0.45 -1,1v1L5,3c-1.11,0 -2,0.9 -2,2v14c0,1.1 0.89,2 2,2h14c1.1,0 2,-0.9 2,-2L21,5c0,-1.1 -0.9,-2 -2,-2zM18,19L6,19c-0.55,0 -1,-0.45 -1,-1L5,8h14v10c0,0.55 -0.45,1 -1,1zM13,14L8,14c-0.55,0 -1,0.45 -1,1s0.45,1 1,1h5c0.55,0 1,-0.45 1,-1s-0.45,-1 -1,-1z"/>
</vector>
