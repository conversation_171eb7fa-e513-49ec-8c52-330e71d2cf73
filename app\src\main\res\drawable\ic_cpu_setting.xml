<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M20,9.6C20,5.6 18.4,4 14.4,4H9.6C5.6,4 4,5.6 4,9.6V14.4C4,18.4 5.6,20 9.6,20"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M16.35,8C15.8,7.3 14.88,7 13.5,7H10.5C8,7 7,8 7,10.5V13.5C7,14.88 7.3,15.8 7.99,16.35"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M8.01,4V2"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M12,4V2"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M16,4V2"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20,8H22"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M8.01,20V22"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M2,8H4"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M2,12H4"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M2,16H4"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M16.709,18.59C17.587,18.59 18.299,17.878 18.299,17C18.299,16.122 17.587,15.41 16.709,15.41C15.831,15.41 15.119,16.122 15.119,17C15.119,17.878 15.831,18.59 16.709,18.59Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11.41,17.46V16.53C11.41,15.98 11.86,15.53 12.41,15.53C13.37,15.53 13.76,14.85 13.28,14.02C13,13.54 13.17,12.92 13.65,12.65L14.56,12.12C14.98,11.87 15.52,12.02 15.77,12.44L15.83,12.54C16.31,13.37 17.09,13.37 17.57,12.54L17.63,12.44C17.88,12.02 18.42,11.88 18.84,12.12L19.75,12.65C20.23,12.93 20.4,13.54 20.12,14.02C19.64,14.85 20.03,15.53 20.99,15.53C21.54,15.53 21.99,15.98 21.99,16.53V17.46C21.99,18.01 21.54,18.46 20.99,18.46C20.03,18.46 19.64,19.14 20.12,19.97C20.4,20.45 20.23,21.07 19.75,21.34L18.84,21.87C18.42,22.12 17.88,21.97 17.63,21.55L17.57,21.45C17.09,20.62 16.31,20.62 15.83,21.45L15.77,21.55C15.52,21.97 14.98,22.11 14.56,21.87L13.65,21.34C13.17,21.06 13,20.45 13.28,19.97C13.76,19.14 13.37,18.46 12.41,18.46C11.86,18.47 11.41,18.02 11.41,17.46Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#292D32"
      android:strokeLineCap="round"/>
</vector>
