<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/_10sdp">

    <TextView
        android:id="@+id/tv_home_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_5sdp"
        android:text="@string/app_name"
        android:textColor="?ns_title"
        android:textSize="@dimen/_14sdp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <LinearLayout
        android:id="@+id/ll_home_view_all"
        android:layout_marginEnd="@dimen/_10sdp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="?attr/selectableItemBackground"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_home_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_home_title" >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/_5sdp"
            android:text="See All"
            android:textAlignment="viewStart"
            android:textColor="?ns_title_sub"
            android:textSize="@dimen/_13sdp"
            tools:ignore="HardcodedText" />

        <ImageView
            android:layout_width="@dimen/_18sdp"
            android:layout_height="@dimen/_18sdp"
            android:background="@drawable/bg_arrow_right"
            android:contentDescription="@string/todo"
            android:src="@drawable/ic_arrow_right"
            app:tint="?ns_white" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_home_cat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_home_title"/>

</androidx.constraintlayout.widget.ConstraintLayout>