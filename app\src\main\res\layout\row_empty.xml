<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/_10sdp">

    <TextView
        android:id="@+id/tv_empty_msg"
        android:layout_marginBottom="@dimen/_10sdp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/_5sdp"
        android:text="@string/err_no_data_found"
        android:textColor="?ns_title"
        android:textSize="@dimen/_13ssp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/ll_empty_try"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="false"
        android:background="@drawable/btn_empty"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_empty"
            android:layout_width="@dimen/_35sdp"
            android:layout_height="@dimen/_35sdp"
            android:padding="@dimen/_9sdp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_refresh"
            app:tint="?ns_primary"
            android:contentDescription="@string/todo"/>

        <ProgressBar
            android:id="@+id/pb_empty"
            android:layout_width="@dimen/_35sdp"
            android:layout_height="@dimen/_35sdp"
            android:padding="@dimen/_7sdp"
            android:indeterminate="true"
            android:indeterminateTint="?ns_primary"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_empty"
            android:text="@string/refresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?ns_primary"
            android:textSize="@dimen/_14ssp"
            android:layout_marginEnd="@dimen/_10sdp"/>

    </LinearLayout>


</LinearLayout>