<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/imageCover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/todo"
        android:src="#1B1B1B" />

    <TextView
        android:id="@+id/textNowPlay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="@string/cast_play_custom"
        android:textColor="@color/white"
        android:textSize="16sp" />

</RelativeLayout>