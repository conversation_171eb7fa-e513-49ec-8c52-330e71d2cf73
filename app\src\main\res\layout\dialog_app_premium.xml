<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_25sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_dialog"
        android:padding="@dimen/_5sdp"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_1sdp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_dialog_icon"
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_vip_diamond"
                app:tint="?ns_title"
                android:contentDescription="@string/todo"/>

            <TextView
                android:id="@+id/tv_dialog_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="@dimen/_9sdp"
                android:text="@string/app_name"
                android:textColor="?ns_title"
                android:textSize="@dimen/_13ssp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_dialog_close"
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_close"
                app:tint="?ns_title"
                android:contentDescription="@string/todo"/>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_dialog_msg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_10sdp"
            android:gravity="center"
            android:text="@string/premium_note"
            android:textColor="?ns_title_sub"
            android:textSize="@dimen/_13ssp" />

        <LinearLayout
            android:background="@drawable/bg_edit_text_feedback"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_10sdp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:padding="@dimen/_7sdp"
                app:srcCompat="@drawable/ic_list_check"
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                app:tint="?ns_bg_dark"
                android:contentDescription="@string/todo" />

            <LinearLayout
                android:layout_gravity="center"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content">

                <TextView
                    android:textSize="@dimen/_11ssp"
                    android:textStyle="bold"
                    android:textColor="?ns_title"
                    android:text="@string/premium_a_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <TextView
                    android:textSize="@dimen/_10ssp"
                    android:textColor="?ns_title_sub"
                    android:text="@string/premium_a_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:background="@drawable/bg_edit_text_feedback"
            android:layout_marginBottom="@dimen/_5sdp"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:padding="@dimen/_5sdp"
                app:srcCompat="@drawable/ic_advertisement"
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                app:tint="?ns_bg_dark"
                android:contentDescription="@string/todo" />

            <LinearLayout
                android:layout_gravity="center"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content">

                <TextView
                    android:textSize="@dimen/_11ssp"
                    android:textStyle="bold"
                    android:textColor="?ns_title"
                    android:text="@string/premium_b_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <TextView
                    android:textSize="@dimen/_10ssp"
                    android:textColor="?ns_title_sub"
                    android:text="@string/premium_b_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </LinearLayout>


        <TextView
            android:id="@+id/tv_dialog_no"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_30sdp"
            android:gravity="center"
            android:text="@string/subscribe_now"
            android:textColor="?ns_white"
            android:textSize="@dimen/_13ssp"
            android:layout_margin="@dimen/_5sdp"
            android:background="@drawable/btn_primary"/>

    </LinearLayout>

</RelativeLayout>