<resources xmlns:tools="http://schemas.android.com/tools">

    <!--   ThemeDialog  -->
    <style name="dialogTheme" parent="@style/Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <!-- Used for the title and text -->
        <item name="android:textColor">?ns_title</item>
        <item name="android:textColorAlertDialogListItem">?ns_title</item>
        <item name="android:textColorSecondary">?ns_title</item>
        <!-- Used for the background -->
        <item name="android:background">?ns_dialog_bg</item>
        <!-- Used for the Button -->
        <item name="buttonBarNegativeButtonStyle">@style/ButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">@style/ButtonStyle</item>
    </style>

    <style name="ButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">?ns_title</item>
    </style>

    <style name="dialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up_dialog</item>
        <item name="android:windowExitAnimation">@anim/slide_down_dialog</item>
    </style>

    <style name="ratingBarWhite" parent="Widget.AppCompat.RatingBar.Small">
        <item name="colorControlNormal">?ns_title_sub</item>
        <item name="colorControlActivated">?ns_primary</item>
    </style>

    <style name="ratingBarDialog" parent="Theme.AppCompat">
        <item name="colorControlNormal">?ns_title_sub</item>
        <item name="colorControlActivated">#FF9800</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" >
        <item name="colorAccent">@color/white</item>
    </style>

    <style name="TextViewNormal" parent="">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="PopupMenuLight" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="PopupMenuDark" parent="ThemeOverlay.AppCompat.Dark"/>

    <style name="CircularProgressWhite" parent="Theme.AppCompat.Light">
        <item name="colorAccent">@color/white</item>
    </style>

    <style name="Theme.CastVideosDark" parent="Theme.AppCompat.NoActionBar">
        <item name="colorPrimaryDark">@android:color/black</item>
        <item name="castMiniControllerStyle">@style/CustomCastMiniController</item>
        <item name="castIntroOverlayStyle">@style/CustomCastIntroOverlay</item>
        <item name="castExpandedControllerStyle">@style/CustomCastExpandedController</item>
        <item name="castExpandedControllerToolbarStyle">
            @style/ThemeOverlay.AppCompat.Dark.ActionBar
        </item>
    </style>

    <style name="CustomCastIntroOverlay" parent="CastIntroOverlay">
        <item name="castButtonTextAppearance">@style/TextAppearance.CustomCastIntroOverlay.Button
        </item>
        <item name="castTitleTextAppearance">@style/TextAppearance.CustomCastIntroOverlay.Title
        </item>
    </style>

    <style name="TextAppearance.CustomCastIntroOverlay.Button" parent="android:style/TextAppearance">
        <item name="android:textColor">@android:color/white</item>
    </style>

    <style name="TextAppearance.CustomCastIntroOverlay.Title" parent="android:style/TextAppearance.Large">
        <item name="android:textColor">@android:color/white</item>
    </style>

    <style name="CustomCastMiniController" parent="CastMiniController">
        <item name="castShowImageThumbnail">true</item>
        <item name="castTitleTextAppearance">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="castSubtitleTextAppearance">@style/TextAppearance.AppCompat.Caption</item>
    </style>

    <style name="CustomCastExpandedController" parent="CastExpandedController" />

</resources>