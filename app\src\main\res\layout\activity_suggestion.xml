<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?ns_bg_sub">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="@drawable/ic_close"
        app:title="@string/suggestion"
        app:titleCentered="true" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_bottom"
        android:layout_below="@+id/toolbar">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_sub"
                android:orientation="vertical"
                android:padding="@dimen/_10sdp">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/enter_your_title_here_suggestion"
                    android:inputType="text"
                    android:padding="@dimen/_9sdp"
                    android:textSize="@dimen/_12ssp"
                    android:textColor="?ns_title"
                    android:textColorHint="?ns_title_sub"
                    android:background="@drawable/bg_card_edit_text"
                    android:importantForAutofill="no"
                    android:imeOptions="actionDone"/>

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/enter_your_description_here_suggestion"
                    android:gravity="top"
                    android:maxLines="20"
                    android:minLines="4"
                    android:padding="@dimen/_9sdp"
                    android:textSize="@dimen/_12ssp"
                    android:textColor="?ns_title"
                    android:textColorHint="?ns_title_sub"
                    android:background="@drawable/bg_card_edit_text"
                    tools:ignore="Autofill,TextFields"
                    android:importantForAutofill="no"
                    android:layout_marginTop="@dimen/_10sdp"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/bg_sub"
                android:orientation="vertical"
                android:padding="@dimen/_10sdp">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/select_image"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_13ssp"
                    android:layout_marginBottom="@dimen/_10sdp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_card_edit_text"
                    android:orientation="horizontal"
                    android:padding="@dimen/_10sdp">

                    <androidx.nemosofts.material.ImageHelperView
                        android:id="@+id/iv_sugg"
                        android:layout_width="@dimen/_90sdp"
                        android:layout_height="@dimen/_90sdp"
                        android:src="@drawable/logo"
                        app:hv_border_color="?ns_border"
                        app:hv_corner_radius="@dimen/_5sdp"
                        android:scaleType="centerCrop"/>

                    <LinearLayout
                        android:id="@+id/ll_sugg"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="horizontal"
                        tools:ignore="UseCompoundDrawables">

                        <ImageView
                            android:layout_width="@dimen/_25sdp"
                            android:layout_height="@dimen/_25sdp"
                            android:src="@drawable/ic_add"
                            app:tint="?ns_title"
                            android:contentDescription="@string/todo"/>

                        <TextView
                            android:textColor="?ns_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_5sdp"
                            android:text="@string/select_image"
                            android:textSize="@dimen/_12ssp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <View
        android:layout_below="@id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1sdp"
        android:background="?ns_border" />

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="?ns_bg"
        android:orientation="vertical">

        <TextView
            android:id="@+id/btn_sugg_submit"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_35sdp"
            android:layout_margin="@dimen/_10sdp"
            android:background="@drawable/btn_danger"
            android:gravity="center"
            android:text="@string/submit"
            android:textColor="?ns_white"
            android:textSize="@dimen/_13ssp"
            android:textStyle="bold"
            tools:ignore="HardcodedText" />

        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/ll_adView"
            android:background="?ns_bg"
            android:backgroundTint="?ns_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1sdp"
        android:layout_above="@+id/ll_bottom"
        android:background="?ns_border" />

</RelativeLayout>