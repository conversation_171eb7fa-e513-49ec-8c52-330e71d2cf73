<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/_110sdp"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/_5sdp"
    android:focusable="true"
    android:focusableInTouchMode="false"
    android:background="@drawable/bg_card_selector"
    android:padding="@dimen/_1sdp"
    android:id="@+id/rl_live_tv">

    <androidx.nemosofts.material.ImageHelperView
        android:id="@+id/iv_live"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_110sdp"
        android:scaleType="centerCrop"
        android:src="@drawable/test_live"
        android:padding="@dimen/_1sdp"
        app:hv_corner_radius="@dimen/_3sdp"
        android:layout_margin="@dimen/_3sdp" />

    <TextView
        android:id="@+id/tv_live_premium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_1sdp"
        android:background="@drawable/bg_pre_home"
        android:paddingLeft="@dimen/_5sdp"
        android:paddingRight="@dimen/_5sdp"
        android:text="Premium"
        android:textColor="@color/white"
        android:textSize="@dimen/_12ssp" />

    <TextView
        android:layout_below="@id/iv_live"
        android:padding="@dimen/_4sdp"
        android:layout_marginStart="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_5sdp"
        android:id="@+id/tv_live"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:textAlignment="center"
        android:lineSpacingMultiplier="0.9"
        android:maxLines="1"
        android:text="@string/app_name"
        android:textColor="?ns_title"
        android:textSize="@dimen/_11ssp"/>

</RelativeLayout>