<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginStart="@dimen/_5sdp"
    android:layout_marginEnd="@dimen/_10sdp"
    android:background="@drawable/bg_about_border"
    android:layout_marginTop="@dimen/_40sdp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="@dimen/_10sdp">

    <androidx.nemosofts.material.ImageHelperView
        app:hv_corner_radius="@dimen/_3sdp"
        android:layout_width="@dimen/_50sdp"
        android:layout_height="@dimen/_50sdp"
        android:src="@drawable/logo" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10sdp"
        android:orientation="vertical"
        android:layout_gravity="center">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="?ns_title"
            android:textSize="@dimen/_12ssp"
            android:textStyle="bold" />

        <TextView
            android:layout_marginTop="@dimen/_2sdp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/made_with_in"
            android:textColor="?ns_title_sub"
            android:textStyle="bold"
            android:textSize="@dimen/_9ssp"/>

    </LinearLayout>

</LinearLayout>