<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rl_video_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_5sdp"
        android:paddingStart="@dimen/_10sdp"
        android:paddingEnd="@dimen/_10sdp"
        android:background="@drawable/bg_player_control_top">

        <ImageView
            android:id="@+id/iv_back_player"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_6sdp"
            android:layout_marginTop="@dimen/_6sdp"
            android:layout_marginEnd="@dimen/_6sdp"
            android:layout_marginBottom="@dimen/_6sdp"
            android:background="@drawable/bg_back_player"
            android:contentDescription="@string/todo"
            android:padding="@dimen/_5sdp"
            android:src="@drawable/ic_player_back"
            app:tint="@color/white"
            android:focusable="false"
            android:focusableInTouchMode="false"/>

        <TextView
            android:id="@+id/tv_player_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/iv_back_player"
            android:padding="@dimen/_10sdp"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="@dimen/_13ssp"
            android:textStyle="bold"
            tools:ignore="RelativeOverlap" />

        <ImageView
            android:id="@+id/iv_battery_info"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_marginStart="@dimen/_6sdp"
            android:layout_centerVertical="true"
            android:padding="@dimen/_4sdp"
            android:src="@drawable/ic_battery_charging"
            android:layout_toStartOf="@+id/iv_media_info"
            android:contentDescription="@string/todo"/>

        <ImageView
            android:id="@+id/iv_media_info"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_marginStart="@dimen/_6sdp"
            android:layout_centerVertical="true"
            android:padding="@dimen/_5sdp"
            android:src="@drawable/ic_information"
            android:layout_toStartOf="@+id/exo_resize"
            android:contentDescription="@string/todo"/>

        <ImageView
            android:id="@+id/exo_resize"
            android:layout_width="@dimen/_30sdp"
            android:layout_height="@dimen/_30sdp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_6sdp"
            android:layout_marginEnd="@dimen/_5sdp"
            android:contentDescription="@string/todo"
            android:padding="@dimen/_6sdp"
            android:src="@drawable/ic_crop" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:layout_centerInParent="true"
        android:alpha="0.8"
        android:background="@drawable/bg_back_player"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/_5sdp"
        android:src="@drawable/ic_play" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/bg_player_control"
        android:paddingTop="@dimen/_10sdp"
        android:paddingBottom="@dimen/_10sdp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:orientation="horizontal"
            tools:ignore="UselessParent">

            <TextView
                android:id="@id/exo_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:includeFontPadding="false"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:text="0.00"
                android:textColor="@color/white"
                android:textSize="@dimen/_8ssp"
                android:textStyle="bold"
                tools:ignore="HardcodedText" />

            <androidx.media3.ui.DefaultTimeBar
                android:id="@id/exo_progress"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_18sdp"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/exo_duration"
                android:layout_toEndOf="@+id/exo_position"
                app:ad_marker_color="#343434"
                app:played_ad_marker_color="#343434"
                app:scrubber_drawable="@drawable/switch_thumb_material"
                app:scrubber_enabled_size="24dp"
                tools:ignore="SpeakableTextPresentCheck" />

            <TextView
                android:id="@id/exo_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginEnd="5dp"
                android:layout_toStartOf="@+id/img_full_scr"
                android:includeFontPadding="false"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:text="0.00"
                android:textColor="@color/white"
                android:textSize="@dimen/_8ssp"
                android:textStyle="bold"
                tools:ignore="HardcodedText" />

            <ImageView
                android:id="@+id/img_full_scr"
                android:layout_width="@dimen/_30sdp"
                android:layout_height="@dimen/_30sdp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_5sdp"
                android:contentDescription="@string/todo"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_fullscreen" />

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>