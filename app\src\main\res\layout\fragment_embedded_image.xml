<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="#1B1B1B"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/imageCover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/app_name"
        android:scaleType="fitXY" />

    <ImageView
        android:id="@+id/imagePlay"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerInParent="true"
        android:contentDescription="@string/app_name"
        android:background="@drawable/abs_history_playlist"
        android:backgroundTint="#88000000"
        android:src="@drawable/ic_play" />

</RelativeLayout>