<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.WebActivity"
    android:background="?ns_bg">

    <LinearLayout
        android:layout_above="@+id/ll_adView"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?ns_bg"
            app:titleTextColor="?ns_bg_dark"
            app:navigationIconTint="?ns_bg_dark"
            app:navigationIcon="@drawable/ic_close"/>

        <ProgressBar
            android:id="@+id/pb_web"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_marginBottom="@dimen/_minus5sdp"
            android:layout_marginTop="@dimen/_minus5sdp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <WebView
            android:id="@+id/web"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <FrameLayout
            android:visibility="gone"
            android:id="@+id/fl_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </LinearLayout>

    <LinearLayout
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:id="@+id/ll_adView"
        android:background="@color/white"
        android:backgroundTint="@color/white"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</RelativeLayout>