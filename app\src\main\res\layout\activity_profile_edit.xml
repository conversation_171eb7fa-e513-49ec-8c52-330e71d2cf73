<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.ProfileEditActivity"
    android:background="?ns_bg">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="@drawable/ic_close"
        app:title="@string/edit_account"
        app:titleCentered="true" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_bottom"
        android:layout_below="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/_20sdp"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rl_profile_edit"
                    android:layout_width="@dimen/_125sdp"
                    android:layout_height="@dimen/_125sdp"
                    tools:ignore="UselessParent">

                    <androidx.nemosofts.material.ImageHelperView
                        android:id="@+id/iv_profile_edit"
                        android:layout_width="@dimen/_105sdp"
                        android:layout_height="@dimen/_105sdp"
                        android:layout_centerInParent="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/user_photo"
                        app:hv_border_color="?ns_bg_sub"
                        app:hv_border_width="@dimen/_1sdp"
                        app:hv_corner_radius="@dimen/_60sdp" />

                    <RelativeLayout
                        android:layout_marginBottom="@dimen/_minus10sdp"
                        android:layout_centerHorizontal="true"
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_30sdp"
                        android:layout_alignBottom="@+id/iv_profile_edit"
                        android:background="@drawable/bg_profile_edit_btn">

                        <ImageView
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:layout_centerInParent="true"
                            android:contentDescription="@string/todo"
                            android:padding="@dimen/_3sdp"
                            android:src="@drawable/ic_camera"
                            app:tint="?ns_bg" />

                    </RelativeLayout>

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="@dimen/_15sdp"
                android:paddingEnd="@dimen/_15sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/_3sdp"
                        android:text="@string/full_name"
                        android:textColor="?ns_title_sub"
                        android:textSize="@dimen/_10ssp" />

                    <EditText
                        android:id="@+id/editText_profedit_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColorHint="?ns_title_sub"
                        android:background="@android:color/transparent"
                        android:hint="@string/full_name"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_13ssp"
                        tools:ignore="Autofill" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:textColor="?ns_title_sub"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/_3sdp"
                        android:text="@string/email_id"
                        android:textSize="@dimen/_10ssp" />

                    <EditText
                        android:id="@+id/editText_profedit_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:hint="@string/email_id"
                        android:textColorHint="?ns_title_sub"
                        android:inputType="textEmailAddress"
                        android:maxLines="1"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_13ssp"
                        tools:ignore="Autofill" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:textColor="?ns_title_sub"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/_3sdp"
                        android:text="@string/password"
                        android:textSize="@dimen/_10ssp" />

                    <EditText
                        android:id="@+id/editText_profedit_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:hint="@string/password"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:textColorHint="?ns_title_sub"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_13ssp"
                        tools:ignore="Autofill" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:textColor="?ns_title_sub"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/_3sdp"
                        android:text="@string/confirm_password"
                        android:textSize="@dimen/_10ssp" />

                    <EditText
                        android:id="@+id/editText_profedit_cpassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:hint="@string/confirm_password"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:textColorHint="?ns_title_sub"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_13ssp"
                        tools:ignore="Autofill" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:textColor="?ns_title_sub"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/_3sdp"
                        android:text="@string/telephone_number"
                        android:textSize="@dimen/_10ssp" />

                    <EditText
                        android:id="@+id/editText_profedit_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:hint="@string/telephone_number"
                        android:inputType="phone"
                        android:textColorHint="?ns_title_sub"
                        android:maxLines="1"
                        android:paddingTop="@dimen/_5sdp"
                        android:paddingBottom="@dimen/_5sdp"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_13ssp"
                        tools:ignore="Autofill" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?ns_bg"
        android:orientation="vertical"
        android:layout_alignParentBottom="true">

        <LinearLayout
            android:id="@+id/ll_update_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_35sdp"
            android:background="@drawable/btn_primary"
            android:layout_margin="@dimen/_10sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                app:tint="@color/white"
                android:padding="@dimen/_2sdp"
                android:src="@drawable/ic_refresh"
                android:layout_marginEnd="@dimen/_5sdp"
                android:contentDescription="@string/todo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/submit"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp" />

        </LinearLayout>

        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/ll_adView"
            android:background="?ns_bg"
            android:backgroundTint="?ns_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

</RelativeLayout>