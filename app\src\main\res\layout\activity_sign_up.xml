<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.SignUpActivity"
    android:background="?ns_bg">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="@dimen/_15sdp"
            android:paddingEnd="@dimen/_15sdp">

            <com.google.android.material.appbar.MaterialToolbar
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?ns_bg"
                app:titleTextColor="?ns_bg_dark"
                app:title="@string/sign_up"
                app:titleCentered="true" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rl_sign_up_pro"
                    android:layout_width="@dimen/_125sdp"
                    android:layout_height="@dimen/_125sdp">

                    <androidx.nemosofts.material.ImageHelperView
                        android:id="@+id/iv_profile_sign"
                        android:layout_width="@dimen/_105sdp"
                        android:layout_height="@dimen/_105sdp"
                        android:layout_centerInParent="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/user_photo"
                        app:hv_border_color="?ns_bg_sub"
                        app:hv_border_width="@dimen/_1sdp"
                        app:hv_corner_radius="@dimen/_60sdp" />

                    <RelativeLayout
                        android:layout_marginBottom="@dimen/_minus10sdp"
                        android:layout_centerHorizontal="true"
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_30sdp"
                        android:layout_alignBottom="@+id/iv_profile_sign"
                        android:background="@drawable/bg_profile_edit_btn">

                        <ImageView
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:layout_centerInParent="true"
                            android:contentDescription="@string/todo"
                            android:padding="@dimen/_3sdp"
                            android:src="@drawable/ic_camera"
                            app:tint="?ns_bg" />

                    </RelativeLayout>

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/_35sdp"
                    android:layout_height="@dimen/_35sdp"
                    android:contentDescription="@string/todo"
                    android:padding="@dimen/_9sdp"
                    android:src="@drawable/ic_email"
                    app:tint="?ns_title_sub" />

                <EditText
                    android:id="@+id/et_register_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:hint="@string/email_id"
                    android:importantForAutofill="no"
                    android:inputType="textEmailAddress"
                    android:maxLines="1"
                    android:padding="@dimen/_5sdp"
                    android:textColor="?ns_title"
                    android:textColorHint="?ns_title_sub"
                    android:textSize="@dimen/_12ssp" />

            </LinearLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginStart="@dimen/_35sdp"
                android:background="?ns_border" />

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/_35sdp"
                    android:layout_height="@dimen/_35sdp"
                    android:contentDescription="@string/todo"
                    android:padding="@dimen/_9sdp"
                    android:src="@drawable/ic_profile"
                    app:tint="?ns_title_sub" />

                <EditText
                    android:id="@+id/et_register_full_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:hint="@string/full_name"
                    android:importantForAutofill="no"
                    android:inputType="textPersonName"
                    android:maxLines="1"
                    android:padding="@dimen/_5sdp"
                    android:textColor="?ns_title"
                    android:textColorHint="?ns_title_sub"
                    android:textSize="@dimen/_12ssp" />

            </LinearLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginStart="@dimen/_35sdp"
                android:background="?ns_border" />

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/_35sdp"
                    android:layout_height="@dimen/_35sdp"
                    android:contentDescription="@string/todo"
                    android:padding="@dimen/_9sdp"
                    android:src="@drawable/ic_phone"
                    app:tint="?ns_title_sub" />

                <EditText
                    android:id="@+id/et_register_telephone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:hint="@string/telephone_number"
                    android:importantForAutofill="no"
                    android:inputType="phone"
                    android:maxLines="1"
                    android:padding="@dimen/_5sdp"
                    android:textColor="?ns_title"
                    android:textColorHint="?ns_title_sub"
                    android:textSize="@dimen/_12ssp" />

            </LinearLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginStart="@dimen/_35sdp"
                android:background="?ns_border" />

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/_35sdp"
                    android:layout_height="@dimen/_35sdp"
                    android:contentDescription="@string/todo"
                    android:padding="@dimen/_9sdp"
                    android:src="@drawable/ic_lock"
                    app:tint="?ns_title_sub" />

                <EditText
                    android:id="@+id/et_register_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:hint="@string/password"
                    android:importantForAutofill="no"
                    android:inputType="textPassword"
                    android:maxLines="1"
                    android:padding="@dimen/_5sdp"
                    android:textColor="?ns_title"
                    android:textColorHint="?ns_title_sub"
                    android:textSize="@dimen/_12ssp" />

            </LinearLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginStart="@dimen/_35sdp"
                android:background="?ns_border" />

            <LinearLayout
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/_35sdp"
                    android:layout_height="@dimen/_35sdp"
                    android:contentDescription="@string/todo"
                    android:padding="@dimen/_9sdp"
                    android:src="@drawable/ic_lock"
                    app:tint="?ns_title_sub" />

                <EditText
                    android:id="@+id/et_register_confirm_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:hint="@string/confirm_password"
                    android:importantForAutofill="no"
                    android:inputType="textPassword"
                    android:maxLines="1"
                    android:padding="@dimen/_5sdp"
                    android:textColor="?ns_title"
                    android:textColorHint="?ns_title_sub"
                    android:textSize="@dimen/_12ssp" />

            </LinearLayout>

            <View
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_1sdp"
                android:layout_marginStart="@dimen/_35sdp"
                android:background="?ns_border" />

            <RadioGroup
                android:id="@+id/radioGrp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:padding="10dp">

                <RadioButton
                    android:id="@+id/rd_male"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:paddingStart="@dimen/_1sdp"
                    android:paddingEnd="@dimen/_15sdp"
                    android:text="@string/male"
                    android:textAlignment="viewStart"
                    android:textColor="?ns_title_sub" />

                <RadioButton
                    android:id="@+id/rd_female"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:includeFontPadding="false"
                    android:paddingStart="@dimen/_1sdp"
                    android:paddingEnd="@dimen/_15sdp"
                    android:text="@string/female"
                    android:textAlignment="viewStart"
                    android:textColor="?ns_title_sub" />

            </RadioGroup>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/by_signing_up_re_agree_to_oue"
                android:textColor="?ns_title_sub"
                android:textSize="@dimen/_11ssp"
                android:layout_marginTop="@dimen/_8sdp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:gravity="start"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_terms"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:background="?attr/selectableItemBackground"
                    android:text="@string/terms_and_conditions"
                    android:textColor="?ns_primary"
                    android:textSize="@dimen/_11ssp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/and"
                    android:textColor="?ns_title_sub"
                    android:textSize="@dimen/_11ssp" />

                <TextView
                    android:id="@+id/tv_privacy_policy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:background="?attr/selectableItemBackground"
                    android:text="@string/privacy_policy"
                    android:textColor="?ns_primary"
                    android:textSize="@dimen/_11ssp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btn_register"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_35sdp"
                android:background="@drawable/btn_danger"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/_20sdp"
                    android:layout_height="@dimen/_20sdp"
                    app:tint="@color/white"
                    android:src="@drawable/ic_user_add"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:contentDescription="@string/todo" />

                <TextView
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/register"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_15ssp"
                    android:textStyle="bold"/>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/ll_login_sign"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/already_have_an_account"
                    android:textColor="?ns_title_sub"
                    android:textSize="@dimen/_11sdp" />

                <TextView
                    android:id="@+id/tv_login_signup"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="5dp"
                    android:background="?attr/selectableItemBackground"
                    android:text="@string/login"
                    android:textColor="?colorAccent"
                    android:textSize="@dimen/_11sdp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</RelativeLayout>