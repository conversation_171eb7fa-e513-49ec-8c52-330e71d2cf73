<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.MainActivity"
    android:background="?ns_bg">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="@drawable/ic_menu"
        app:title="@string/app_name"
        app:titleCentered="true" />

    <FrameLayout
        android:id="@+id/fragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_bottom_nav"
        android:layout_below="@+id/toolbar" />

    <LinearLayout
        android:id="@+id/ll_bottom_nav"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="@dimen/_3sdp"
        android:layout_marginBottom="@dimen/_1sdp">

        <androidx.nemosofts.material.ToggleView
            android:layoutDirection="ltr"
            android:id="@+id/tv_nav_home"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bt_active="true"
            app:bt_colorActive="?ns_primary"
            app:bt_colorInactive="?ns_bg_dark"
            app:bt_icon="@drawable/ic_home"
            app:bt_shape="@drawable/transition_bg_menu"
            app:bt_title="@string/nav_home" />

        <androidx.nemosofts.material.ToggleView
            android:layoutDirection="ltr"
            android:id="@+id/tv_nav_latest"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bt_colorActive="?ns_primary"
            app:bt_colorInactive="?ns_bg_dark"
            app:bt_icon="@drawable/ic_list_check"
            app:bt_shape="@drawable/transition_bg_menu"
            app:bt_title="@string/latest" />

        <androidx.nemosofts.material.ToggleView
            android:layoutDirection="ltr"
            android:id="@+id/tv_nav_most"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bt_colorActive="?ns_primary"
            app:bt_colorInactive="?ns_bg_dark"
            app:bt_icon="@drawable/ic_flashlight"
            app:bt_shape="@drawable/transition_bg_menu"
            app:bt_title="@string/trending" />

        <androidx.nemosofts.material.ToggleView
            android:layoutDirection="ltr"
            android:id="@+id/tv_nav_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bt_colorActive="?ns_primary"
            app:bt_colorInactive="?ns_bg_dark"
            app:bt_icon="@drawable/ic_folders"
            app:bt_shape="@drawable/transition_bg_menu"
            app:bt_title="@string/categories" />

        <androidx.nemosofts.material.ToggleView
            android:layoutDirection="ltr"
            android:id="@+id/tv_nav_restore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:bt_colorActive="?ns_primary"
            app:bt_colorInactive="?ns_bg_dark"
            app:bt_icon="@drawable/ic_history"
            app:bt_shape="@drawable/transition_bg_menu"
            app:bt_title="@string/recently" />

    </LinearLayout>

</RelativeLayout>