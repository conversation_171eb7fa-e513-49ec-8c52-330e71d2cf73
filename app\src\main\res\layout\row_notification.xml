<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?ns_bg"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_marginBottom="@dimen/_5sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:background="@drawable/bg_dialog"
        android:padding="@dimen/_10sdp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_not_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_toStartOf="@+id/rl_not_close"
            android:text="@string/app_name"
            android:textColor="?ns_title"
            android:textSize="@dimen/_13ssp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_not_note"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_not_title"
            android:layout_alignParentStart="true"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_toStartOf="@+id/rl_not_close"
            android:text="@string/app_name"
            android:textColor="?ns_title_sub"
            android:textSize="@dimen/_13ssp" />

        <TextView
            android:id="@+id/tv_not_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_not_note"
            android:layout_alignParentStart="true"
            android:layout_marginTop="@dimen/_3sdp"
            android:text="@string/app_name"
            android:textColor="?ns_title_sub"
            android:textSize="@dimen/_11ssp" />

        <View
            android:layout_width="@dimen/_1sdp"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/tv_not_title"
            android:layout_alignBottom="@+id/tv_not_date"
            android:layout_toStartOf="@+id/rl_not_close"
            android:background="?ns_border" />

        <RelativeLayout
            android:id="@+id/rl_not_close"
            android:layout_width="@dimen/_40sdp"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/tv_not_title"
            android:layout_alignBottom="@+id/tv_not_date"
            android:layout_alignParentEnd="true"
            android:gravity="center"
            android:paddingStart="@dimen/_10sdp"
            android:paddingEnd="0dp">

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:contentDescription="@string/todo"
                android:src="@drawable/ic_close"
                app:tint="#E91E63" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/iv_close"
                android:layout_centerHorizontal="true"
                android:text="close"
                android:textColor="?ns_title"
                android:textSize="@dimen/_10ssp"
                tools:ignore="HardcodedText" />

        </RelativeLayout>

    </RelativeLayout>

</LinearLayout>