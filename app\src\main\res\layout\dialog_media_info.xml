<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_media"
        android:layout_alignParentEnd="true"
        android:layout_width="@dimen/_235sdp"
        android:layout_height="match_parent"
        android:background="#CC000000"
        android:orientation="vertical">

        <LinearLayout
            android:layout_margin="@dimen/_10sdp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="@dimen/_3sdp"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/iv_back_player_info"
                android:layout_width="@dimen/_30sdp"
                android:layout_height="@dimen/_30sdp"
                android:contentDescription="@string/todo"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_player_back"
                app:tint="@color/white"
                android:focusable="true"
                android:focusableInTouchMode="false"
                android:background="@drawable/bg_back_player"/>

            <TextView
                android:paddingStart="@dimen/_15sdp"
                android:paddingEnd="@dimen/_15sdp"
                android:textSize="@dimen/_15sdp"
                android:textColor="?ns_white"
                android:textStyle="bold"
                android:text="Information"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_2sdp"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <View
            android:background="#12FFFFFF"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1sdp"/>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_marginEnd="@dimen/_15sdp"
                android:layout_marginStart="@dimen/_15sdp"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:padding="@dimen/_2sdp"
                    android:background="@drawable/bg_card_info"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    tools:ignore="UseCompoundDrawables,UselessParent">

                    <ImageView
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_30sdp"
                        android:contentDescription="@string/todo"
                        android:src="@drawable/ic_series"
                        app:tint="@color/white"
                        android:padding="@dimen/_5sdp"/>

                    <TextView
                        android:text="@string/video_tracks"
                        android:textColor="?ns_white"
                        android:textStyle="bold"
                        android:textSize="@dimen/_15sdp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_info_video"
                    android:text="@string/app_name"
                    android:textColor="?ns_white"
                    android:textSize="@dimen/_13sdp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_3sdp"
                    android:layout_marginEnd="@dimen/_3sdp"/>

                <LinearLayout
                    android:padding="@dimen/_2sdp"
                    android:background="@drawable/bg_card_info"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginBottom="@dimen/_5sdp"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    tools:ignore="UseCompoundDrawables,UselessParent">

                    <ImageView
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_30sdp"
                        android:contentDescription="@string/todo"
                        android:src="@drawable/ic_audiotrack"
                        app:tint="@color/white"
                        android:padding="@dimen/_5sdp"/>

                    <TextView
                        android:text="@string/audio_tracks"
                        android:textColor="?ns_white"
                        android:textStyle="bold"
                        android:textSize="@dimen/_15sdp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_info_audio"
                    android:text="@string/app_name"
                    android:textColor="?ns_white"
                    android:textSize="@dimen/_13sdp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_3sdp"
                    android:layout_marginEnd="@dimen/_3sdp"
                    android:layout_marginBottom="@dimen/_10sdp"/>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <View
        android:id="@+id/iv_close_vw"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_toStartOf="@+id/ll_media" />

</RelativeLayout>