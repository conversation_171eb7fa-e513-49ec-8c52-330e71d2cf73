<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?ns_bg"
    android:layout_marginStart="@dimen/_5sdp"
    android:layout_marginEnd="@dimen/_5sdp">

    <LinearLayout
        android:layout_marginBottom="@dimen/_5sdp"
        android:id="@+id/ll_event_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_event"
        android:orientation="vertical">

        <RelativeLayout
            android:background="@drawable/bg_event_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:padding="@dimen/_7sdp"
                android:id="@+id/tv_event_title"
                android:text="@string/app_name"
                android:textSize="@dimen/_13ssp"
                android:textStyle="bold"
                android:textColor="?ns_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"/>

            <TextView
                android:id="@+id/tv_event_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/_5sdp"
                android:layout_toStartOf="@+id/tv_event_date"
                android:background="@drawable/bg_event"
                android:paddingStart="@dimen/_6sdp"
                android:paddingTop="@dimen/_2sdp"
                android:paddingEnd="@dimen/_6sdp"
                android:paddingBottom="@dimen/_2sdp"
                android:text="10:00"
                android:textColor="?ns_title"
                android:textSize="@dimen/_10ssp" />

            <TextView
                android:id="@+id/tv_event_date"
                android:layout_marginEnd="@dimen/_5sdp"
                android:background="@drawable/bg_event"
                android:layout_alignParentEnd="true"
                android:text="20/10/2023"
                android:textSize="@dimen/_10ssp"
                android:textColor="?ns_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:paddingTop="@dimen/_2sdp"
                android:paddingBottom="@dimen/_2sdp"
                android:paddingStart="@dimen/_6sdp"
                android:paddingEnd="@dimen/_6sdp"/>

        </RelativeLayout>


        <LinearLayout
            android:padding="@dimen/_10sdp"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_team_one"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:layout_toStartOf="@+id/iv_team_one"
                    android:text="@string/app_name"
                    android:textAlignment="viewEnd"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_10ssp"
                    android:textStyle="bold" />

                <androidx.nemosofts.material.ImageHelperView
                    android:layout_alignParentEnd="true"
                    android:id="@+id/iv_team_one"
                    android:src="@drawable/logo"
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    app:hv_border_color="?ns_border"
                    app:hv_border_width="@dimen/_3sdp"
                    app:hv_mutate_background="false"
                    app:hv_oval="true"/>

            </RelativeLayout>

            <RelativeLayout
                android:layout_gravity="center"
                android:layout_weight="0.4"
                android:layout_width="0dp"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/bg_card"
                    android:paddingStart="@dimen/_5sdp"
                    android:paddingTop="@dimen/_2sdp"
                    android:paddingEnd="@dimen/_5sdp"
                    android:paddingBottom="@dimen/_2sdp"
                    android:text="VS"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_10ssp"
                    android:textStyle="bold" />

            </RelativeLayout>


            <RelativeLayout
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content">

                <androidx.nemosofts.material.ImageHelperView
                    android:layout_alignParentStart="true"
                    android:id="@+id/iv_team_two"
                    android:src="@drawable/logo"
                    android:layout_width="@dimen/_45sdp"
                    android:layout_height="@dimen/_45sdp"
                    app:hv_border_color="?ns_border"
                    app:hv_border_width="@dimen/_3sdp"
                    app:hv_mutate_background="false"
                    app:hv_oval="true"/>

                <TextView
                    android:id="@+id/tv_team_two"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_toEndOf="@+id/iv_team_two"
                    android:textAlignment="viewStart"
                    android:text="@string/app_name"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_10ssp"
                    android:textStyle="bold" />

            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_native_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?ns_bg"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginBottom="@dimen/_5sdp"
        android:visibility="gone"/>

</LinearLayout>