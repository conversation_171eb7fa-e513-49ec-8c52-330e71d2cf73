package com.voicebird.tv.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;

import com.voicebird.tv.BuildConfig;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class CrashReporter {
    
    private static final String TAG = "CrashReporter";
    private static final String PREFS_NAME = "crash_reports";
    private static final String KEY_CRASH_COUNT = "crash_count";
    private static final String KEY_LAST_CRASH = "last_crash";
    
    private final Context context;
    private final SharedPreferences prefs;
    
    public CrashReporter(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = this.context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    public void reportCrash(Throwable throwable) {
        try {
            String crashReport = generateCrashReport(throwable);
            saveCrashReport(crashReport);
            Log.e(TAG, "Crash reported: " + crashReport);
        } catch (Exception e) {
            Log.e(TAG, "Error reporting crash", e);
        }
    }
    
    private String generateCrashReport(Throwable throwable) {
        StringBuilder report = new StringBuilder();
        
        // App info
        report.append("=== CRASH REPORT ===\n");
        report.append("App: ").append(context.getPackageName()).append("\n");
        report.append("Version: ").append(BuildConfig.VERSION_NAME)
              .append(" (").append(BuildConfig.VERSION_CODE).append(")\n");
        report.append("Time: ").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
              .format(new Date())).append("\n");
        
        // Device info
        report.append("Device: ").append(Build.MANUFACTURER).append(" ").append(Build.MODEL).append("\n");
        report.append("OS: Android ").append(Build.VERSION.RELEASE)
              .append(" (API ").append(Build.VERSION.SDK_INT).append(")\n");
        
        // Memory info
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        report.append("Memory: ").append(totalMemory - freeMemory).append("/")
              .append(totalMemory).append("/").append(maxMemory).append(" MB\n");
        
        // Exception info
        report.append("Exception: ").append(throwable.getClass().getSimpleName()).append("\n");
        report.append("Message: ").append(throwable.getMessage()).append("\n");
        
        // Stack trace
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        report.append("Stack trace:\n").append(sw.toString());
        
        report.append("=== END CRASH REPORT ===\n");
        
        return report.toString();
    }
    
    private void saveCrashReport(String crashReport) {
        try {
            int crashCount = prefs.getInt(KEY_CRASH_COUNT, 0) + 1;
            prefs.edit()
                 .putInt(KEY_CRASH_COUNT, crashCount)
                 .putString(KEY_LAST_CRASH, crashReport)
                 .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving crash report", e);
        }
    }
    
    public int getCrashCount() {
        return prefs.getInt(KEY_CRASH_COUNT, 0);
    }
    
    public String getLastCrashReport() {
        return prefs.getString(KEY_LAST_CRASH, "No crashes recorded");
    }
    
    public void clearCrashReports() {
        prefs.edit().clear().apply();
    }
}
