<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/lytPlan"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_60sdp"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginBottom="@dimen/_5sdp"
        android:background="@drawable/bg_card_select"
        tools:ignore="UselessParent">

        <RadioButton
            android:id="@+id/radioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_10sdp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_toEndOf="@+id/radioButton"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:includeFontPadding="false"
                    android:text="99"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_20sdp"
                    android:textStyle="bold"
                    tools:ignore="HardcodedText" />

                <TextView
                    android:id="@+id/textCurrency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:includeFontPadding="false"
                    android:text="USD"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_12ssp"
                    tools:ignore="HardcodedText" />

                <TextView
                    android:id="@+id/textDay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:includeFontPadding="false"
                    android:text="For 1 Month"
                    android:textColor="#ff0532"
                    android:textSize="@dimen/_12ssp"
                    tools:ignore="HardcodedText" />

            </LinearLayout>

            <TextView
                android:id="@+id/textPackName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="All Access Pack"
                android:textColor="?ns_title"
                android:textSize="@dimen/_10ssp"
                tools:ignore="HardcodedText" />

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>