<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/_100sdp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_home_card"
    android:layout_height="@dimen/_100sdp"
    android:layout_marginStart="@dimen/_5sdp"
    android:id="@+id/rl_recent">

    <androidx.nemosofts.material.ImageHelperView
        android:padding="@dimen/_1sdp"
        android:id="@+id/iv_recently"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:hv_corner_radius="@dimen/_5sdp"
        android:layout_margin="@dimen/_1sdp"
        android:src="@drawable/material_design_default" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/_4sdp"
        android:layout_marginTop="@dimen/_4sdp"
        android:layout_marginEnd="@dimen/_4sdp"
        android:layout_marginBottom="@dimen/_4sdp"
        android:background="@drawable/bg_re_title"
        android:backgroundTint="#8C000000"
        android:orientation="horizontal"
        android:padding="@dimen/_4sdp">

        <TextView
            android:gravity="center_vertical"
            android:id="@+id/tv_recent_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="5"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="@dimen/_10ssp" />

        <ImageView
            android:id="@+id/iv_play_view"
            android:layout_width="@dimen/_15sdp"
            android:layout_height="@dimen/_15sdp"
            android:layout_gravity="center"
            android:background="@drawable/abs_history_playlist"
            android:contentDescription="@string/todo"
            android:padding="@dimen/_1sdp"
            android:src="@drawable/ic_play" />

    </LinearLayout>


</RelativeLayout>