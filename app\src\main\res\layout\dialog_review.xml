<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_25sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_dialog"
        android:orientation="vertical"
        android:padding="@dimen/_5sdp"
        tools:ignore="UselessParent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_1sdp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:contentDescription="@string/todo"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_rating"
                app:tint="?ns_title" />

            <TextView
                android:id="@+id/tv_rate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="@dimen/_9sdp"
                android:text="@string/rating"
                android:textColor="?ns_title"
                android:textSize="@dimen/_13ssp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:background="@drawable/bar_selector"
                android:contentDescription="@string/todo"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_close_page"
                app:tint="?ns_title" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatRatingBar
            android:id="@+id/rb_add"
            android:layout_marginBottom="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_10sdp"
            style="@style/ratingBarDialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:max="5"
            android:numStars="5"
            android:theme="@style/ratingBarDialog" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_messages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_5sdp"
            android:background="@drawable/bg_edit_text_feedback"
            android:gravity="top"
            android:hint="@string/report_message"
            android:maxLines="10"
            android:minLines="2"
            android:padding="@dimen/_10ssp"
            android:textColor="?ns_title"
            android:textSize="@dimen/_12ssp"
            tools:ignore="Autofill,TextFields" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/_10sdp">

            <TextView
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="@dimen/_30sdp"
                android:layout_weight="1"
                android:background="@drawable/btn_danger"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="?ns_white"
                android:textSize="@dimen/_13ssp"
                android:layout_marginEnd="@dimen/_10sdp"/>

            <TextView
                android:id="@+id/tv_submit"
                android:layout_width="0dp"
                android:layout_height="@dimen/_30sdp"
                android:layout_weight="1"
                android:background="@drawable/btn_primary"
                android:gravity="center"
                android:text="@string/submit"
                android:textColor="?ns_white"
                android:textSize="@dimen/_13ssp" />

        </LinearLayout>

    </LinearLayout>

    <ProgressBar
        android:visibility="gone"
        android:id="@+id/pb_rate"
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</RelativeLayout>