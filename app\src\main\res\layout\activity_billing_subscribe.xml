<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.BillingSubscribeActivity"
    android:background="?ns_bg_sub">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="?ns_icon_back"
        app:title="@string/subscription_plan"
        app:titleCentered="true" />

    <LinearLayout
        android:layout_above="@+id/ll_bottom"
        android:layout_below="@id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:background="?ns_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:gravity="center"
                android:text="@string/subscription_msg"
                android:textColor="?ns_title_sub"
                android:textSize="@dimen/_11sdp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10sdp"
                android:text="@string/select_subscription_plan"
                android:textColor="?ns_title"
                android:textSize="@dimen/_13ssp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/_1sdp"
            android:background="?ns_border" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_plan"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false" />

    </LinearLayout>

    <FrameLayout
        android:visibility="gone"
        android:id="@+id/fl_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_bottom"
        android:layout_centerInParent="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1sdp"
        android:layout_below="@id/toolbar"
        android:background="?ns_border" />

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="?ns_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_terms"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:layout_marginEnd="@dimen/_5sdp"
                android:background="?attr/selectableItemBackground"
                android:text="@string/terms_and_conditions"
                android:textColor="?colorAccent"
                android:textSize="@dimen/_11ssp" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/ll_calculator_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?ns_bg">

            <TextView
                android:id="@+id/tv_btn_proceed"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_35sdp"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_10sdp"
                android:background="@drawable/btn_danger"
                android:gravity="center"
                android:text="@string/subscription_proceed"
                android:textColor="?ns_white"
                android:textSize="@dimen/_13ssp"
                android:textStyle="bold" />

        </RelativeLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1sdp"
        android:layout_above="@+id/ll_bottom"
        android:background="?ns_border" />

    <ProgressBar
        android:id="@+id/pb"
        android:layout_width="@dimen/_40sdp"
        android:layout_height="@dimen/_40sdp"
        android:layout_centerInParent="true"
        android:progressTint="?colorAccent" />

</RelativeLayout>