<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network Permission -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- Notifications Permission -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Storage Permission -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" />
    <!-- Advertising Permission -->
    <uses-permission android:name="android.permission.Ad_ID" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!-- In app purchase Permission -->
    <uses-permission android:name="com.android.vending.BILLING" />

    <application
        android:name=".activity.MyApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Nemosofts.ThemeEngine.DayNight"
        android:usesCleartextTraffic="true"
        tools:targetApi="n">
        <activity
            android:name=".activity.LauncherActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.MainActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SignInActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.SignUpActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.ForgotPasswordActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.ProfileActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ProfileEditActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.WebActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.AboutUsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.SettingActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.DialogActivity"
            android:exported="false" />
        <activity
            android:name=".activity.NotificationActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SuggestionActivity"
            android:exported="false" />
        <activity
            android:name=".activity.PostIDActivity"
            android:exported="false" />
        <activity
            android:name=".activity.BillingConnectorActivity"
            android:exported="false" />
        <activity
            android:name=".activity.BillingSubscribeActivity"
            android:exported="false" />
        <activity
            android:name=".activity.EmbeddedWebViewPlayerActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".activity.EmbeddedYoutubePlayerActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".activity.EmbeddedPlayerActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".activity.VideoDetailsActivity"
            android:configChanges="orientation|screenLayout|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <meta-data
            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
            android:value="nemosofts.online.live.cast.CastOptionsProvider" />

        <activity
            android:name=".cast.ExpandedControlsActivity"
            android:theme="@style/Theme.CastVideosDark" />

        <!-- Mobile Advertising -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/admob_app_id" />
        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize" />
        <meta-data
            android:name="com.startapp.sdk.MIXED_AUDIENCE"
            android:value="true"/>
        <meta-data
            android:name="applovin.sdk.key"
            android:value="@string/applovin_id" />
        <meta-data
            android:name="com.wortise.ads.utm"
            android:value="nsft" />
        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />
        <activity android:name="com.facebook.ads.AudienceNetworkActivity" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <activity
            android:name="com.ironsource.sdk.controller.ControllerActivity"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true" />
        <activity
            android:name="com.ironsource.sdk.controller.InterstitialActivity"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="com.ironsource.sdk.controller.OpenUrlActivity"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="com.ironsource.mediationsdk.testSuite.TestSuiteActivity"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar" />
        <provider
            android:name="com.ironsource.lifecycle.IronsourceLifecycleProvider"
            android:authorities="${applicationId}.IronsourceLifecycleProvider" />
        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/ga_ad_services_config"
            tools:replace="android:resource" />
    </application>

</manifest>