<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <item
        android:id="@+id/nav_home"
        android:icon="@drawable/ic_home"
        android:title="@string/nav_home" />

    <item
        android:id="@+id/nav_latest"
        android:icon="@drawable/ic_list_check"
        android:title="@string/latest" />

    <item
        android:id="@+id/nav_most"
        android:icon="@drawable/ic_flashlight"
        android:title="@string/trending" />

    <item
        android:id="@+id/nav_category"
        android:icon="@drawable/ic_folders"
        android:title="@string/categories" />

    <item
        android:id="@+id/nav_restore"
        android:icon="@drawable/ic_history"
        android:title="@string/recently" />

    <item
        android:id="@+id/nav_event"
        android:icon="@drawable/ic_todo"
        android:title="@string/live_event" />

    <item
        android:id="@+id/nav_fav"
        android:icon="@drawable/ic_heart"
        android:title="@string/favourite" />

    <item
        android:id="@+id/nav_suggest"
        android:icon="@drawable/ic_function_add"
        android:title="@string/suggestion" />

    <item
        android:id="@+id/nav_subscription"
        android:icon="@drawable/ic_vip_diamond"
        android:title="@string/subscription_plan" />

    <item
        android:id="@+id/nav_profile"
        android:icon="@drawable/ic_profile"
        android:title="@string/nav_profile" />

    <item
        android:id="@+id/nav_settings"
        android:icon="@drawable/ic_setting"
        android:title="@string/nav_settings" />

    <item
        android:id="@+id/nav_login"
        android:icon="@drawable/ic_login"
        android:title="@string/login" />

</menu>