<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="@dimen/_24sdp"
    android:height="@dimen/_24sdp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M21,5.98C17.67,5.65 14.32,5.48 10.98,5.48C9,5.48 7.02,5.58 5.04,5.78L3,5.98"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="@color/black"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M8.5,4.97L8.72,3.66C8.88,2.71 9,2 10.69,2H13.31C15,2 15.13,2.75 15.28,3.67L15.5,4.97"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="@color/black"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M15.21,22H8.79C6,22 5.91,20.78 5.8,19.21L5.15,9.14"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="@color/black"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M18.849,9.14L18.199,19.21"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="@color/black"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M10.33,16.5H13.66"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="@color/black"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M12.82,12.5H14.5"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="@color/black"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M9.5,12.5H10.33"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="@color/black"
      android:strokeLineCap="round"/>
</vector>
