<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.ProfileActivity"
    android:background="?ns_bg">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?ns_bg"
        app:titleTextColor="?ns_bg_dark"
        app:navigationIconTint="?ns_bg_dark"
        app:navigationIcon="@drawable/ic_close"
        app:title="@string/profile"
        app:titleCentered="true"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignBottom="@+id/toolbar"
        android:gravity="center_vertical"
        android:layout_marginEnd="@dimen/_8sdp">

        <ImageView
            android:id="@+id/iv_notifications"
            android:layout_width="@dimen/_35sdp"
            android:layout_height="@dimen/_35sdp"
            android:padding="@dimen/_8sdp"
            android:src="@drawable/ic_notification_badge"
            app:tint="?ns_bg_dark"
            android:background="?attr/selectableItemBackground"
            android:contentDescription="@string/todo" />

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_above="@+id/ll_adView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/_20sdp"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rl_profile"
                    android:layout_width="@dimen/_125sdp"
                    android:layout_height="@dimen/_125sdp">

                    <androidx.nemosofts.material.ImageHelperView
                        android:id="@+id/iv_profile"
                        android:layout_width="@dimen/_105sdp"
                        android:layout_height="@dimen/_105sdp"
                        android:layout_centerInParent="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/user_photo"
                        app:hv_border_color="?ns_bg_sub"
                        app:hv_border_width="@dimen/_1sdp"
                        app:hv_corner_radius="@dimen/_60sdp" />

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignStart="@+id/iv_profile"
                        android:layout_alignTop="@+id/iv_profile"
                        android:layout_alignEnd="@+id/iv_profile"
                        android:layout_alignBottom="@+id/iv_profile">

                        <ProgressBar
                            android:visibility="gone"
                            android:id="@+id/pb_iv_profile"
                            android:layout_width="@dimen/_40sdp"
                            android:layout_height="@dimen/_40sdp"
                            android:layout_centerInParent="true" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_marginBottom="@dimen/_minus10sdp"
                        android:layout_centerHorizontal="true"
                        android:layout_width="@dimen/_30sdp"
                        android:layout_height="@dimen/_30sdp"
                        android:layout_alignBottom="@+id/iv_profile"
                        android:background="@drawable/bg_profile_edit_btn">

                        <ImageView
                            android:layout_width="@dimen/_25sdp"
                            android:layout_height="@dimen/_25sdp"
                            android:layout_centerInParent="true"
                            android:contentDescription="@string/todo"
                            android:padding="@dimen/_3sdp"
                            android:src="@drawable/ic_edit"
                            app:tint="?ns_bg" />

                    </RelativeLayout>

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_profile_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?ns_title"
                    android:textSize="@dimen/_15ssp"
                    android:textStyle="bold"
                    android:text="@string/app_name"/>

                <TextView
                    android:id="@+id/tv_profile_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?ns_title_sub"
                    android:text="@string/app_name"
                    android:textSize="@dimen/_12ssp"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="@dimen/_15sdp"
                android:paddingEnd="@dimen/_15sdp">

                <LinearLayout
                    android:id="@+id/ll_policy"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/_3sdp"
                    android:paddingBottom="@dimen/_3sdp">

                    <ImageView
                        android:layout_width="@dimen/_35sdp"
                        android:layout_height="@dimen/_35sdp"
                        android:contentDescription="@string/todo"
                        android:padding="@dimen/_9sdp"
                        android:src="@drawable/ic_shield_keyhole"
                        app:tint="?ns_title" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/privacy_policy"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_12ssp" />

                </LinearLayout>

                <View
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:id="@+id/ll_terms"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/_3sdp"
                    android:paddingBottom="@dimen/_3sdp">

                    <ImageView
                        android:layout_width="@dimen/_35sdp"
                        android:layout_height="@dimen/_35sdp"
                        android:contentDescription="@string/todo"
                        android:padding="@dimen/_9sdp"
                        android:src="@drawable/ic_todo"
                        app:tint="?ns_title" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/terms_and_conditions"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_12ssp" />

                </LinearLayout>

                <View
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:id="@+id/ll_trash"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/_3sdp"
                    android:paddingBottom="@dimen/_3sdp">

                    <ImageView
                        android:layout_width="@dimen/_35sdp"
                        android:layout_height="@dimen/_35sdp"
                        android:contentDescription="@string/todo"
                        android:padding="@dimen/_8sdp"
                        android:src="@drawable/ic_trash"
                        app:tint="?ns_title" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/delete_account"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_12ssp" />

                </LinearLayout>

                <View
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_1sdp"
                    android:background="?ns_border" />

                <LinearLayout
                    android:id="@+id/ll_logout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/_3sdp"
                    android:paddingBottom="@dimen/_3sdp"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:layout_width="@dimen/_35sdp"
                        android:layout_height="@dimen/_35sdp"
                        android:contentDescription="@string/todo"
                        android:padding="@dimen/_8sdp"
                        android:src="@drawable/ic_logout"
                        app:tint="?ns_title" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/_5sdp"
                        android:text="@string/logout"
                        android:textColor="?ns_title"
                        android:textSize="@dimen/_12ssp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:id="@+id/ll_adView"
        android:background="?ns_bg"
        android:backgroundTint="?ns_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</RelativeLayout>