<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/_25sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_dialog"
        android:padding="@dimen/_5sdp"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_1sdp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_dialog_icon"
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_exit"
                app:tint="?ns_title"
                android:contentDescription="@string/todo"/>

            <TextView
                android:id="@+id/tv_dialog_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="@dimen/_9sdp"
                android:text="@string/app_name"
                android:textColor="?ns_title"
                android:textSize="@dimen/_13ssp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_dialog_close"
                android:layout_width="@dimen/_35sdp"
                android:layout_height="@dimen/_35sdp"
                android:padding="@dimen/_5sdp"
                android:src="@drawable/ic_close"
                app:tint="?ns_title"
                android:contentDescription="@string/todo"/>

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_dialog_icon_bg"
            android:layout_width="@dimen/_70sdp"
            android:layout_height="@dimen/_70sdp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_15sdp"
            android:contentDescription="@string/todo"
            android:src="@mipmap/ic_launcher"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_dialog_title_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_10sdp"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="?ns_title_sub"
            android:textSize="@dimen/_14ssp"
            android:textStyle="bold"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_dialog_msg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_10sdp"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="?ns_title_sub"
            android:textSize="@dimen/_13ssp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_5sdp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_dialog_no"
                android:layout_width="0dp"
                android:layout_height="@dimen/_30sdp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/no"
                android:textColor="?ns_white"
                android:textSize="@dimen/_13ssp"
                android:background="@drawable/btn_danger"/>

            <View
                android:id="@+id/vw_dialog_bar"
                android:layout_width="@dimen/_10sdp"
                android:layout_height="@dimen/_1sdp"/>

            <TextView
                android:id="@+id/tv_dialog_yes"
                android:layout_width="0dp"
                android:layout_height="@dimen/_30sdp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/yes"
                android:textColor="?ns_white"
                android:textSize="@dimen/_13ssp"
                android:background="@drawable/btn_primary"/>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>