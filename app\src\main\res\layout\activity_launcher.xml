<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.SignInActivity"
    android:id="@+id/rl_splash"
    android:background="@color/ns_classic_bg">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_splash_card"
        app:cardElevation="@dimen/_3sdp"
        app:cardCornerRadius="@dimen/_5sdp"
        android:layout_width="@dimen/_130sdp"
        android:layout_height="@dimen/_130sdp"
        android:layout_centerInParent="true">

        <ImageView
            android:id="@+id/iv_splash_logo"
            android:src="@drawable/logo"
            android:scaleType="centerCrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/default_todo" />

    </androidx.cardview.widget.CardView>

    <ProgressBar
        android:id="@+id/pb_splash"
        android:layout_width="@dimen/_35sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/_20sdp"/>

</RelativeLayout>