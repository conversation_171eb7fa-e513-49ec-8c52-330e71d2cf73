<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <androidx.nemosofts.material.EnchantedViewPager
        android:id="@+id/viewPager_home"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_160sdp"
        android:layout_marginTop="@dimen/_10sdp" />

    <com.nemosofts.ui.PageIndicatorView
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/viewPager_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingTop="10dp"
        app:piv_animationType="worm"
        app:piv_dynamicCount="true"
        app:piv_interactiveAnimation="true"
        app:piv_padding="5dp"
        app:piv_radius="5dp"
        app:piv_selectedColor="?ns_primary"
        app:piv_unselectedColor="?ns_title_sub"
        app:piv_viewPager="@id/viewPager_home" />

</RelativeLayout>