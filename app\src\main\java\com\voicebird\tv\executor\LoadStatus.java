package com.voicebird.tv.executor;


import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.voicebird.tv.callback.Callback;
import com.voicebird.tv.interfaces.SuccessListener;
import com.voicebird.tv.utils.ApplicationUtil;
import com.voicebird.tv.utils.AsyncTaskExecutor;
import okhttp3.RequestBody;

public class LoadStatus extends AsyncTaskExecutor<String, String, String> {

    private final RequestBody requestBody;
    private final SuccessListener listener;
    private String success = "0";
    private String message = "";

    public LoadStatus(SuccessListener listener, RequestBody requestBody) {
        this.listener = listener;
        this.requestBody = requestBody;
    }

    @Override
    protected void onPreExecute() {
        listener.onStart();
        super.onPreExecute();
    }

    @Override
    protected String doInBackground(String strings) {
        try {
            String json = ApplicationUtil.responsePost(Callback.API_URL, requestBody);

            if (json == null || json.isEmpty()) {
                success = "0";
                message = "No response from server";
                return "0";
            }

            JSONObject mainJson = new JSONObject(json);

            if (!mainJson.has(Callback.TAG_ROOT)) {
                success = "0";
                message = "Invalid server response format";
                return "0";
            }

            JSONArray jsonArray = mainJson.getJSONArray(Callback.TAG_ROOT);

            if (jsonArray.length() == 0) {
                success = "0";
                message = "Empty response from server";
                return "0";
            }

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject c = jsonArray.getJSONObject(i);
                success = c.optString(Callback.TAG_SUCCESS, "0");
                message = c.optString(Callback.TAG_MSG, "Unknown error occurred");
            }
            return "1";
        } catch (JSONException e) {
            success = "0";
            message = "Failed to parse server response";
            Log.e("LoadStatus", "JSON parsing error: " + e.getMessage());
            return "0";
        } catch (Exception e) {
            success = "0";
            message = "Network error occurred";
            Log.e("LoadStatus", "Network error: " + e.getMessage());
            return "0";
        }
    }

    @Override
    protected void onPostExecute(String s) {
        listener.onEnd(s, success, message);
    }
}