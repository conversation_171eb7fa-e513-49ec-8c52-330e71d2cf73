package com.voicebird.tv.executor;


import org.json.JSONArray;
import org.json.JSONObject;

import com.voicebird.tv.callback.Callback;
import com.voicebird.tv.interfaces.SuccessListener;
import com.voicebird.tv.utils.ApplicationUtil;
import com.voicebird.tv.utils.AsyncTaskExecutor;
import okhttp3.RequestBody;

public class LoadStatus extends AsyncTaskExecutor<String, String, String> {

    private final RequestBody requestBody;
    private final SuccessListener listener;
    private String success = "0";
    private String message = "";

    public LoadStatus(SuccessListener listener, RequestBody requestBody) {
        this.listener = listener;
        this.requestBody = requestBody;
    }

    @Override
    protected void onPreExecute() {
        listener.onStart();
        super.onPreExecute();
    }

    @Override
    protected String doInBackground(String strings) {
        try {
            String json = ApplicationUtil.responsePost(Callback.API_URL, requestBody);
            JSONObject mainJson = new JSONObject(json);
            JSONArray jsonArray = mainJson.getJSONArray(Callback.TAG_ROOT);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject c = jsonArray.getJSONObject(i);
                success = c.getString(Callback.TAG_SUCCESS);
                message = c.getString(Callback.TAG_MSG);
            }
            return "1";
        } catch (Exception e) {
            return "0";
        }
    }

    @Override
    protected void onPostExecute(String s) {
        listener.onEnd(s, success, message);
    }
}